import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  ScrollView,
  Dimensions,
} from 'react-native';
import * as ImagePickerExpo from 'expo-image-picker';
import { colors } from '@/constants/colors';
import { Camera, Image as ImageIcon, X, Plus } from 'lucide-react-native';
import { useTranslation } from '@/i18n/useTranslation';

const { width: screenWidth } = Dimensions.get('window');
const imageWidth = (screenWidth - 64) / 3; // 3 images per row with padding

interface MultipleImagePickerProps {
  label?: string;
  images: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
  placeholder?: string;
  required?: boolean;
  error?: string;
  showAnalyzeButton?: boolean;
  onAnalyzeImage?: (imageUri: string) => void;
  analyzingImage?: boolean;
}

export const MultipleImagePicker: React.FC<MultipleImagePickerProps> = ({
  label,
  images,
  onImagesChange,
  maxImages = 3,
  placeholder = 'Add Images',
  required = false,
  error,
  showAnalyzeButton = false,
  onAnalyzeImage,
  analyzingImage = false,
}) => {
  const [loading, setLoading] = useState(false);
  const { t, isRTL } = useTranslation();

  const requestPermissions = async () => {
    const cameraPermission = await ImagePickerExpo.requestCameraPermissionsAsync();
    const mediaPermission = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
    
    if (cameraPermission.status !== 'granted' || mediaPermission.status !== 'granted') {
      Alert.alert(
        t('common.permissionRequired'),
        t('common.cameraGalleryPermission'),
        [{ text: t('common.ok') }]
      );
      return false;
    }
    return true;
  };

  const showImagePickerOptions = () => {
    if (images.length >= maxImages) {
      Alert.alert(
        t('common.maxImagesReached'),
        t('common.maxImagesMessage', { max: maxImages }),
        [{ text: t('common.ok') }]
      );
      return;
    }

    Alert.alert(
      t('common.selectImage'),
      t('common.chooseImageSource'),
      [
        { text: t('common.camera'), onPress: takePhoto },
        { text: t('common.gallery'), onPress: pickFromGallery },
        { text: t('common.cancel'), style: 'cancel' },
      ]
    );
  };

  const takePhoto = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      setLoading(true);
      const result = await ImagePickerExpo.launchCameraAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets?.length > 0) {
        const newImages = [...images, result.assets[0].uri];
        onImagesChange(newImages);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.imagePickError'));
    } finally {
      setLoading(false);
    }
  };

  const pickFromGallery = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      setLoading(true);
      const result = await ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: ImagePickerExpo.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets?.length > 0) {
        const newImages = [...images, result.assets[0].uri];
        onImagesChange(newImages);
      }
    } catch (error) {
      Alert.alert(t('common.error'), t('common.imagePickError'));
    } finally {
      setLoading(false);
    }
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onImagesChange(newImages);
  };

  const renderImageItem = (imageUri: string, index: number) => (
    <View key={index} style={styles.imageContainer}>
      <Image source={{ uri: imageUri }} style={styles.image} />
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => removeImage(index)}
      >
        <X size={16} color={colors.white} />
      </TouchableOpacity>
      {showAnalyzeButton && index === 0 && onAnalyzeImage && (
        <TouchableOpacity
          style={styles.analyzeButton}
          onPress={() => onAnalyzeImage(imageUri)}
          disabled={analyzingImage}
        >
          {analyzingImage ? (
            <ActivityIndicator size="small" color={colors.white} />
          ) : (
            <Text style={styles.analyzeButtonText}>AI</Text>
          )}
        </TouchableOpacity>
      )}
    </View>
  );

  const renderAddButton = () => {
    if (images.length >= maxImages) return null;

    return (
      <TouchableOpacity
        style={styles.addButton}
        onPress={showImagePickerOptions}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator size="small" color={colors.primary} />
        ) : (
          <>
            <Plus size={24} color={colors.gray[400]} />
            <Text style={styles.addButtonText}>{placeholder}</Text>
          </>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {label && (
        <View style={[styles.labelContainer, isRTL && styles.rtlDirection]}>
          <Text style={[styles.label, isRTL && styles.rtlTextAlign]}>
            {label}
          </Text>
          {required && <Text style={styles.required}>*</Text>}
        </View>
      )}

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
      >
        {images.map((imageUri, index) => renderImageItem(imageUri, index))}
        {renderAddButton()}
      </ScrollView>

      <Text style={styles.helperText}>
        {t('common.maxImagesHelper', { current: images.length, max: maxImages })}
      </Text>

      {error && (
        <Text style={[styles.errorText, isRTL && styles.rtlTextAlign]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  labelContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
  },
  required: {
    color: colors.danger,
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  rtlDirection: {
    flexDirection: 'row-reverse',
  },
  rtlTextAlign: {
    textAlign: 'right',
  },
  scrollContainer: {
    paddingHorizontal: 4,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  image: {
    width: imageWidth,
    height: imageWidth,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
  },
  removeButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: colors.danger,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  analyzeButton: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: colors.primary,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  analyzeButtonText: {
    fontSize: 10,
    color: colors.white,
    fontWeight: 'bold',
  },
  addButton: {
    width: imageWidth,
    height: imageWidth,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: colors.gray[300],
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  addButtonText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 4,
    textAlign: 'center',
  },
  helperText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 8,
  },
  errorText: {
    color: colors.danger,
    fontSize: 12,
    marginTop: 4,
  },
});

export default MultipleImagePicker;
