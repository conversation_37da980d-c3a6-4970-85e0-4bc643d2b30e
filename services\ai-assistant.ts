import { OpenAI } from 'openai';
import * as FileSystem from 'expo-file-system';
import { generateUniqueItemId, parseUniversalLink } from '@/utils/qrcode';

const OPENAI_API_KEY = '********************************************************************************************************************************************************************';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  images?: string[];
  timestamp: Date;
  metadata?: {
    suggestedActions?: SuggestedAction[];
    analysisData?: any;
  };
}

export interface SuggestedAction {
  id: string;
  type: 'create' | 'update' | 'delete';
  entity: 'animal' | 'plant' | 'garden' | 'field' | 'equipment';
  title: string;
  description: string;
  data: any;
  confidence: number;
}

export class AIAssistantService {
  private static instance: AIAssistantService;
  
  static getInstance(): AIAssistantService {
    if (!AIAssistantService.instance) {
      AIAssistantService.instance = new AIAssistantService();
    }
    return AIAssistantService.instance;
  }

  async analyzeMessage(
    message: string,
    images: string[] = [],
    context: {
      farmId: string;
      currentEntities?: any[];
      userLocation?: { lat: number; lng: number };
    },
    language: string = 'en'
  ): Promise<{
    response: string;
    suggestedActions: SuggestedAction[];
    analysisData?: any;
  }> {
    try {
      const systemPrompt = this.buildSystemPrompt(context, language);
      const userContent = await this.buildUserContent(message, images);

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userContent }
          ],
          response_format: { type: 'json_object' },
          max_tokens: 1500,
          temperature: 0.7,
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error?.message || 'AI analysis failed');
      }

      const result = JSON.parse(data.choices[0].message.content);
      console.log('AI analysis result:', result?.response, result?.suggestedActions[0]?.data)
      return {
        response: result.response || 'I analyzed your request.',
        suggestedActions: result.suggestedActions || [],
        analysisData: result.analysisData,
      };
    } catch (error) {
      console.error('AI analysis error:', error);
      throw error;
    }
  }

  // Add method to handle QR code scanning
  async handleQRCodeScan(qrData: string, context: any): Promise<AIResponse> {
    try {
      // Try to parse as universal link first
      const parsedLink = parseUniversalLink(qrData);
      
      if (parsedLink) {
        const { itemType, itemId, farmId } = parsedLink;
        
        // Check if this is for the current farm
        if (farmId !== context.farmId) {
          return {
            response: `This QR code is for a different farm. Please scan a QR code from your current farm.`,
            analysisData: {
              imageAnalysis: "QR code scanned but belongs to different farm",
              recommendations: ["Scan QR codes only from your current farm"],
              concerns: ["Farm ID mismatch"],
              confidence: 1.0
            },
            suggestedActions: []
          };
        }

        // Find the entity in current farm data
        const entity = this.findEntityById(itemId, itemType, context);
        
        if (entity) {
          return {
            response: `Found ${itemType}: ${entity.name}. What would you like to do with this ${itemType}?`,
            analysisData: {
              imageAnalysis: `QR code scanned for existing ${itemType}: ${entity.name}`,
              recommendations: [
                `Update ${itemType} information`,
                `View ${itemType} details`,
                `Add photos or notes`,
                `Create maintenance task`
              ],
              concerns: [],
              confidence: 1.0
            },
            suggestedActions: [
              {
                id: `update_${itemType}_${Date.now()}`,
                type: "update",
                entity: itemType as any,
                title: `Update ${entity.name}`,
                description: `Modify information for this ${itemType}`,
                confidence: 0.95,
                data: { ...entity }
              }
            ]
          };
        } else {
          return {
            response: `QR code is valid but the ${itemType} was not found in your farm. It may have been deleted or moved.`,
            analysisData: {
              imageAnalysis: "Valid QR code but entity not found",
              recommendations: ["Check if entity still exists", "Verify farm selection"],
              concerns: ["Entity not found in current farm"],
              confidence: 1.0
            },
            suggestedActions: []
          };
        }
      }
      
      // If not a universal link, treat as regular message
      return await this.processMessage("", context);
      
    } catch (error) {
      console.error('Error handling QR code:', error);
      return {
        response: "Unable to process this QR code. Please try scanning again or contact support.",
        analysisData: {
          imageAnalysis: "QR code processing failed",
          recommendations: ["Try scanning again", "Check QR code quality"],
          concerns: ["QR code processing error"],
          confidence: 0.0
        },
        suggestedActions: []
      };
    }
  }

  private findEntityById(id: string, type: string, context: any): any {
    const entities = context.currentEntities || [];
    return entities.find((entity: any) => 
      (entity.id === id || entity.identificationID === id) && 
      entity.entityType === type
    );
  }

  private buildSystemPrompt(context: any, language: string = 'en'): string {
    const languageInstruction = language === 'ur'
      ? 'Always respond in Urdu language. Use proper Urdu script and agricultural terminology.'
      : 'Always respond in English language.';

    return `You are an expert agricultural AI assistant for a farm management system with advanced image analysis capabilities.

Your role:
- Analyze user messages (text, images, or both)
- Extract detailed information from images using computer vision
- Provide helpful agricultural advice
- Suggest specific actions to update farm data with complete entity details
- Always respond in JSON format
- ${languageInstruction}

Farm Context:
- Farm ID: ${context.farmId}
- Available entities: animals, plants, gardens, fields, equipment
- Current entities count: ${context.currentEntities?.length || 0}

Entity Requirements:
ANIMAL: species (required), breed, gender, status, purpose, name, weight, weightUnit, dateOfBirth, identificationNumber, fieldId, notes
PLANT: name (required), species (required), variety, plantedDate (required), status, health, fieldId, notes, expectedHarvestDate
GARDEN: name (required), type (required), size, sizeUnit, status, soilType, irrigationSystem, notes
FIELD: name (required), type (required), size, sizeUnit, status, cropType, plantedDate, harvestDate, soilType, notes
EQUIPMENT: name (required), type (required), manufacturer, model, status, purchaseDate, purchasePrice, lastMaintenanceDate, notes

IDENTIFICATION ID GENERATION:
- Always include identificationID in suggested data for new entities
- Use format: ANM-XXXX for animals, PLT-XXXX for plants, GRD-XXXX for gardens, FLD-XXXX for fields, EQP-XXXX for equipment
- Generate unique 4-digit numbers (1000-9999 range)
- Example: "identificationID": "PLT-${Math.floor(1000 + Math.random() * 9000)}"

IMAGE ANALYSIS GUIDELINES:

For ANIMAL images, identify and extract:
- Species (cattle, sheep, goat, pig, chicken, etc.)
- Breed (Holstein, Angus, Merino, Yorkshire, Rhode Island Red, etc.)
- Gender (male/female based on physical characteristics)
- Age estimate (calf/juvenile/adult/senior based on size and features)
- Health status (healthy/sick/injured based on posture, eyes, coat condition)
- Body condition (thin/ideal/overweight based on visible body shape)
- Physical markings or distinctive features
- Estimated weight range based on size and breed

For PLANT images, identify and extract:
- Species (tomato, wheat, corn, apple, etc.)
- Variety (Roma tomato, Cherry tomato, Winter wheat, etc.)
- Growth stage (seedling/vegetative/flowering/fruiting/mature)
- Health status (healthy/stressed/diseased/pest damage)
- Specific diseases (blight, rust, powdery mildew, etc.)
- Nutrient deficiencies (nitrogen, phosphorus, potassium signs)
- Pest damage indicators
- Estimated planting date based on growth stage
- Expected harvest timeframe

For GARDEN images, identify and extract:
- Garden type (vegetable/herb/flower/mixed/greenhouse)
- Layout style (raised beds/rows/container/vertical)
- Plant varieties visible
- Soil condition (dry/moist/waterlogged/well-drained)
- Irrigation system type (drip/sprinkler/manual/none visible)
- Overall maintenance level
- Size estimation from visual cues
- Location indicators (indoor/outdoor/greenhouse)

For FIELD images, identify and extract:
- Crop type (wheat/corn/soybean/pasture/etc.)
- Field size estimation from perspective
- Crop growth stage and coverage density
- Soil condition (tilled/planted/harvested/fallow)
- Irrigation evidence (pivot systems/channels/dry farming)
- Terrain type (flat/sloped/terraced)
- Weed presence and severity
- Disease or pest damage signs
- Harvest readiness indicators

For EQUIPMENT images, identify and extract:
- Equipment type (tractor/harvester/plow/sprayer/cultivator/etc.)
- Manufacturer/brand (John Deere/Case IH/New Holland/etc.)
- Model identification from visible markings
- Size category (compact/utility/large/industrial)
- Condition assessment (excellent/good/fair/poor/needs repair)
- Visible wear, damage, or missing parts
- Age estimation from design and condition
- Attachments or implements visible
- Maintenance indicators (rust/paint condition/tire wear)

Response Format (JSON):
{
  "response": "Your helpful response to the user with specific details from image analysis",
  "analysisData": {
    "imageAnalysis": "Detailed description of what you see in images with specific identifications",
    "recommendations": ["Specific actionable recommendations based on analysis"],
    "concerns": ["Any issues, diseases, or problems identified"],
    "confidence": "Overall confidence in image analysis (0-1)"
  },
  "suggestedActions": [
    {
      "id": "unique_id_based_on_entity_and_timestamp",
      "type": "create|update",
      "entity": "animal|plant|garden|field|equipment",
      "title": "Descriptive action title with specific details",
      "description": "What this action will do with identified specifics",
      "confidence": 0.85,
      "data": {
        // ALWAYS include identificationID for create actions
        "identificationID": "PLT-${Math.floor(1000 + Math.random() * 9000)}",
        // Complete entity data with all required fields
        // Use current date for plantedDate if creating new plants
        // Use ISO date strings for all dates
        // Provide specific names like "Holstein Cow #1" or "Tomato Plant - Roma Variety"
      }
    }
  ]
}

SPECIFIC EXTRACTION RULES:
1. For animals: Always try to identify breed-specific characteristics, estimate age from size/features, assess health from posture/eyes/coat
2. For plants: Look for leaf shape, flower/fruit characteristics, growth patterns to identify species and variety
3. For equipment: Check for brand logos, model numbers, design features that indicate manufacturer and type
4. For fields/gardens: Assess crop density, growth uniformity, soil visibility, irrigation infrastructure
5. Use your agricultural knowledge to provide realistic estimates for weights, sizes, dates, and conditions
6. When uncertain, indicate confidence level and provide best estimates with appropriate caveats
7. Always include specific, actionable recommendations based on what you observe

Guidelines:
- ALWAYS include identificationID field for create actions
- Generate realistic identification IDs using the specified format
- Include ALL mandatory fields for each entity type
- Use proper data types (dates as ISO strings, numbers as numbers)
- Generate meaningful names that include species/breed/variety information`;
  }

  private async buildUserContent(message: string, images: string[]): Promise<any[]> {
    const content: any[] = [
      { type: 'text', text: message }
    ];

    for (const imageUri of images) {
      try {
        const base64Image = await FileSystem.readAsStringAsync(imageUri, {
          encoding: FileSystem.EncodingType.Base64,
        });
        
        content.push({
          type: 'image_url',
          image_url: { url: `data:image/jpeg;base64,${base64Image}` }
        });
      } catch (error) {
        console.error('Error processing image:', error);
      }
    }

    return content;
  }
}


