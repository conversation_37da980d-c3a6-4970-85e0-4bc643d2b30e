import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  RefreshControl,
  Alert,
  Modal,
  FlatList,
  ActivityIndicator,
  Image,
  I18nManager,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/auth-store';
import { getLookupTitle, useFarmStore } from '@/store/farm-store';
import {
  CheckCircle,
  Clock,
  Home,
  ChevronDown,
  Check,
  AlertTriangle,
  Plus,
  Users,
  BarChart3,
  Leaf,
  Rabbit,
  Tractor,
} from 'lucide-react-native';
import StatCard from '@/components/StatCard';
import WeatherCard from '@/components/WeatherCard';
import TaskCard from '@/components/TaskCard';
import FieldCard from '@/components/FieldCard';
import { Farm } from '@/types';
import { useTranslation } from '@/i18n/useTranslation';
import RTLView from '@/components/RTLView';
import { useLanguage } from '@/i18n/translations/LanguageProvider';
import { RealTimeSyncIndicator } from '@/components/RealTimeSyncIndicator';

export default function HomeScreen() {
  // const {setLocale, isRTL } = useLanguage();
  const { user, getUsersByFarm

  } = useAuthStore();
  const {
    currentFarm,
    setCurrentFarm,
    farms,
    fetchFarms,
    tasks,
    fields,
    weather,
    gardens,
    animals,
    plants,
    yields,
    equipment,
    fetchTasks,
    fetchFields,
    fetchWeather,
    fetchGardens,
    fetchAnimals,
    fetchPlants,
    fetchYields,
    fetchEquipment,
    isLoading,
    disableRealTimeSync,
    enableRealTimeSync,
  } = useFarmStore();
  // const fetchLookups = useLookupStore(state => state.fetchLookups);
  const { lookupsList, fetchLookups } = useLookupStore();
  //  useEff
  useEffect(() => {
    // This will run once when the app layout mounts,
    // fetching all your lookups in the background.
    fetchLookups();
  }, [fetchLookups]);
  const { t, isRTL } = useTranslation();

  const [refreshing, setRefreshing] = useState(false);
  const [showFarmModal, setShowFarmModal] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [farmsLoaded, setFarmsLoaded] = useState(false);
  // Sync locale with auth store language
  // useEffect(() => {
  //   if (user?.preferredLanguage && user.preferredLanguage !== locale) {
  //     setLocale(user.preferredLanguage);
  //   }
  // }, [user, locale, setLocale]);
  useEffect(() => {
    if (user?.id) {
      setInitialLoading(true);
      fetchFarms(user.id).finally(() => {
        setFarmsLoaded(true);
        setInitialLoading(false);
      });
    }
  }, [user]);

  useEffect(() => {
    if (farms.length > 0 && !currentFarm) {
      setCurrentFarm(farms[0].id);
    }
  }, [farms, currentFarm]);

  useEffect(() => {
    if (currentFarm?.id) {
      // Enable real-time sync for the selected farm
      enableRealTimeSync(currentFarm.id);

      // Initial data load
      Promise.all([
        fetchTasks(currentFarm.id),
        fetchFields(currentFarm.id),
        fetchGardens(currentFarm.id),
        fetchAnimals(currentFarm.id),
        fetchPlants(currentFarm.id),
        fetchYields(currentFarm.id),
        fetchEquipment(currentFarm.id),
        ...(currentFarm.location ? [fetchWeather(currentFarm.location)] : []),
      ]).catch(error => {
        console.error('Error loading initial farm data:', error);
      });
    } else {
      // Disable real-time sync when no farm is selected
      disableRealTimeSync();
    }

    // Cleanup function
    return () => {
      if (!currentFarm?.id) {
        disableRealTimeSync();
      }
    };
  }, [currentFarm?.id]);

  // Add cleanup when component unmounts
  useEffect(() => {
    return () => {
      disableRealTimeSync();
    };
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    if (user?.id) {
      await fetchFarms(user.id);
    }
    if (currentFarm?.id) {
      await Promise.all([
        fetchTasks(currentFarm.id),
        fetchFields(currentFarm.id),
        ...(currentFarm.location ? [fetchWeather(currentFarm.location)] : []),
        fetchGardens(currentFarm.id),
        fetchAnimals(currentFarm.id),
        fetchPlants(currentFarm.id),
        fetchYields(currentFarm.id),
        fetchEquipment(currentFarm.id)
      ]);
    }
    setRefreshing(false);
  };


  useEffect(()=>{
    if(lookupsList.length > 0){
      onRefresh();
    }
  },[lookupsList])
  // Get pending tasks

  const pendingTasks = tasks.filter(task =>
    task.status === 'pending' &&
    (user?.role === 'caretaker' ? task.assignedTo === user.id : true)
  ).sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
  // Get recent activity
  const recentActivity = tasks
    .filter(task => task.status === 'completed')
    .sort((a, b) => {
      const dateA = new Date(a.completedAt || a.updatedAt);
      const dateB = new Date(b.completedAt || b.updatedAt);
      return dateB.getTime() - dateA.getTime();
    })
    .slice(0, 3);
  const [farmUsers, setFarmUsers] = useState<User[]>([]);
  const [loadingFarmUsers, setLoadingFarmUsers] = useState(false);

  useEffect(() => {
    if (currentFarm && currentFarm?.id)
      loadFarmUsers(currentFarm?.id)
  }, [currentFarm?.id])
  const loadFarmUsers = async (farmId: string) => {
    setLoadingFarmUsers(true);
    try {
      const users = await getUsersByFarm(farmId);
      setFarmUsers(users);
    } catch (error) {
      console.error('Error loading farm users:', error);
    } finally {
      setLoadingFarmUsers(false);
    }
  };
  // Get field health stats
  // const fieldHealthStats = {
  //   good: fields.filter(field => field?.health === 'good').length,
  //   average: fields.filter(field => field?.health === 'average').length,
  //   poor: fields.filter(field => field?.health === 'poor').length,
  // };


  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) {
      return t('home.goodMorning');
    } else if (hour < 18) {
      return t('home.goodAfternoon');
    } else {
      return t('home.goodEvening');
    }
  };

  const renderFarmItem = ({ item }: { item: Farm }) => (
    <TouchableOpacity
      style={[styles.modalItem, isRTL && { flexDirection: 'row-reverse' }]}
      onPress={() => {
        setCurrentFarm(item?.id);
        setShowFarmModal(false);
      }}
    >
      <View style={[styles.modalItemContent, isRTL && { flexDirection: 'row-reverse' }]}>
        <Home size={20} color={colors.primary} style={styles.modalItemIcon} />
        <Text style={[styles.modalItemText, isRTL && { marginRight: '8' }]}>{item.name}</Text>
      </View>
      {currentFarm?.id === item.id && (
        <Check size={20} color={colors.success} />
      )}
    </TouchableOpacity>
  );

  if (initialLoading || (isLoading && !refreshing && !farmsLoaded)) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>{t('common.loading')}</Text>
      </SafeAreaView>
    );
  }

  if (farmsLoaded && farms.length === 0) {
    return (
      <SafeAreaView style={styles.noFarmContainer}>
        <AlertTriangle size={60} color={colors.warning} />
        <Text style={styles.noFarmTitle}>{t('farm.noFarms')}</Text>
        <Text style={styles.noFarmText}>{t('farm.createFarmPrompt')}</Text>
        <Button
          title={t('farm.createFarm')}
          onPress={() => router.push('/farm/create')}
          style={styles.createFarmButton}
        // isRTL={isRTL}
        />
      </SafeAreaView>
    );
  }



  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={[styles.header, { flexDirection:isRTL? 'row-reverse' : 'row',justifyContent: 'space-between' }]}>
          <View style={[styles.header]}>
            <Text style={[styles.greeting, { textAlign: isRTL ? 'right' : 'left' }]}>{getGreeting()}</Text>
            <Text style={[styles.userName, { textAlign: isRTL ? 'right' : 'left' }]}>{user?.name || user?.displayName}</Text>

          </View>
          <RealTimeSyncIndicator />

        </View>

        {/* Farm Selection */}
        {farms.length > 0 && (
          <TouchableOpacity
            style={[styles.farmSelectionButton, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}
            onPress={() => setShowFarmModal(true)}
          >
            <Home size={20} color={colors.primary} style={styles.farmIcon} />
            <Text style={[styles.farmSelectionText, { textAlign: isRTL ? 'right' : 'left', marginHorizontal: 8 }]}>
              {currentFarm?.name || t('farm.selectFarm')}
            </Text>
            <ChevronDown size={20} color={colors.primary} />
          </TouchableOpacity>
        )}


        {currentFarm && (
          <View style={styles.farmOverviewCard}>
            <View style={[styles.farmOverviewHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <Text style={styles.farmOverviewTitle}>{currentFarm.name}</Text>
              <TouchableOpacity
                style={styles.farmDetailsButton}
                onPress={() => router.push(`/farm/${currentFarm.id}`)}
              >
                <Text style={styles.farmDetailsText}>{t('common.detail','Details')}</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.farmOverviewInfo}>
              <View style={[styles.farmInfoItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Text style={[styles.farmInfoLabel, { textAlign: isRTL ? 'right' : 'left' }]}>{t('farm.location')}:</Text>
                <Text style={[styles.farmInfoValue, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {currentFarm.location || t('common.notSet')}
                </Text>

              </View>

              <View style={[styles.farmInfoItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Text style={[styles.farmInfoLabel, { textAlign: isRTL ? 'right' : 'left' }]}>{t('farm.size')}:</Text>
                <Text style={[styles.farmInfoValue, { textAlign: isRTL ? 'right' : 'left' }]}>
                  {currentFarm.size} {t(`common.areaUnit.${currentFarm?.sizeUnit?.slice(0, 1).toUpperCase() + currentFarm?.sizeUnit?.slice(1).toLowerCase()}`,currentFarm.sizeUnit)}
                  {/* //currentFarm.sizeUnit} */}
                </Text>
              </View>

              <View style={[styles.farmInfoItem, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Text style={[styles.farmInfoLabel, { textAlign: isRTL ? 'right' : 'left' }]}>{t('farm.type')}:</Text>
                <Text style={[styles.farmInfoValue, { textAlign: isRTL ? 'right' : 'left' }]}>{t(`farm.farmtypes.${currentFarm.type?.slice(0, 1).toUpperCase() + currentFarm.type?.slice(1).toLowerCase()}`,currentFarm.type)}</Text>
              </View>
            </View>

            <View style={[styles.farmQuickStats, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <View style={styles.farmQuickStatItem}>
                <Leaf size={16} color={colors.success} />
                <Text style={styles.farmQuickStatValue}>{fields.length}</Text>
                <Text style={styles.farmQuickStatLabel}>{t('entity.field.plural')}</Text>
              </View>

              <View style={styles.farmQuickStatItem}>
                <Rabbit size={16} color={colors.warning} />
                <Text style={styles.farmQuickStatValue}>{animals.length}</Text>
                <Text style={styles.farmQuickStatLabel}>{t('entity.animal.plural')}</Text>
              </View>

              <View style={styles.farmQuickStatItem}>
                <Tractor size={16} color={colors.info} />
                <Text style={styles.farmQuickStatValue}>{equipment ? equipment.length : 0}</Text>
                <Text style={styles.farmQuickStatLabel}>{t('entity.equipment.plural')}</Text>
              </View>

              <View style={styles.farmQuickStatItem}>
                <Users size={16} color={colors.primary} />
                <Text style={styles.farmQuickStatValue}>
                  {/* This would be the count of users with permission to this farm */}
                  {user?.role === 'owner' ? farmUsers?.length || '--' : '-'}
                </Text>
                <Text style={styles.farmQuickStatLabel}>{t('user.plural')}</Text>
              </View>
            </View>
          </View>
        )}
        {/* {weather && weather.length > 0 && (
          <View style={styles.section}>
            <View style={[styles.sectionHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <Text style={styles.sectionTitle}>
                {t('home.weatherForecast')}
              </Text>
              <TouchableOpacity>
                <Text style={styles.seeAllText}>
                  {t('common.seeAll')}
                </Text>
              </TouchableOpacity>
            </View>

            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.weatherContainer}
            >
              {weather.map((day, index) => (
                <WeatherCard
                  key={index}
                  date={new Date(day.date)}
                  temperature={day.temperature}
                  condition={day.condition}
                  humidity={day.humidity}
                  windSpeed={day.windSpeed}
                // isRTL={isRTL}
                />
              ))}
            </ScrollView>
          </View>
        )} */}

        {user?.role === 'caretaker' ? (
          <View style={styles.section}>
            <RTLView style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>
                {t('home.yourTasks')}
              </Text>
              <TouchableOpacity onPress={() => router.push('/tasks')}>
                <Text style={styles.seeAllText}>
                  {t('common.seeAll')}
                </Text>
              </TouchableOpacity>
            </RTLView>

            {pendingTasks.length > 0 ? (
              pendingTasks.slice(0, 3).map((task) => (
                <TaskCard
                  key={task.id}
                  task={task}
                  onPress={() => router.push(`/task/[id]?id=${task.id}`)}
                />
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  {t('home.noPendingTasks')}
                </Text>
              </View>
            )}
          </View>
        ) : (
          <>
            <View style={styles.section}>
              <View style={[styles.sectionHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Text style={styles.sectionTitle}>
                  {t('home.fieldStatus')}
                </Text>
                <TouchableOpacity onPress={() => router.push('/map')}>
                  <Text style={styles.seeAllText}>
                    {t('common.seeAll')}
                  </Text>
                </TouchableOpacity>
              </View>

              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                // style={{ flexDirection: isRTL ? 'row-reverse' : 'row' }}
                contentContainerStyle={[styles.fieldsContainer]}
              >
                {fields.length > 0 ? (
                  fields.map((field) => (
                    <FieldCard
                      key={field.id}
                      field={field}
                      onPress={() => router.push(`/field/${field.id}`)}
                    // isRTL={isRTL}
                    />
                  ))
                ) : (
                  <View style={styles.emptyFieldContainer}>
                    <Text style={styles.emptyText}>
                      {t('entity.field.noFieldsAdded')}
                    </Text>
                  </View>
                )}
              </ScrollView>
            </View>

            <View style={styles.section}>
              <View style={[styles.sectionHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                <Text style={styles.sectionTitle}>
                  {t('tasks.pending')}
                </Text>
                <TouchableOpacity onPress={() => router.push('/tasks')}>
                  <Text style={styles.seeAllText}>
                    {t('common.seeAll')}
                  </Text>
                </TouchableOpacity>
              </View>

              {pendingTasks.length > 0 ? (
                pendingTasks.slice(0, 3).map((task) => (
                  <TaskCard
                    key={task.id}
                    task={task}
                    onPress={() => router.push(`/task/[id]?id=${task.id}`)}
                  // isRTL={isRTL}
                  />
                ))
              ) : (
                <View style={styles.emptyContainer}>
                  <Text style={styles.emptyText}>
                    {t('tasks.noTasks')}
                  </Text>
                </View>
              )}
            </View>
          </>
        )}

        <View style={styles.section}>
          <View style={[styles.sectionHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
            <Text style={styles.sectionTitle}>
              {t('home.recentActivities')}
            </Text>
          </View>

          {recentActivity.length > 0 ? (
            recentActivity.map((activity, index) => (
              <View key={index} style={styles.activityItem}>
                <View style={styles.activityIconContainer}>
                  <CheckCircle size={20} color={colors.success} />
                </View>
                <View style={styles.activityContent}>
                  <Text style={styles.activityTitle}>
                    {activity.title}
                  </Text>
                  <Text style={styles.activityTime}>
                    {new Date(activity.completedAt || activity.updatedAt).toLocaleString([], {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: true,
                    })}
                    {' • '}
                    {t('tasks.completed')}
                  </Text>
                </View>
              </View>
            ))
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>
                {t('home.noRecentActivity')}
              </Text>
            </View>
          )}
        </View>

        {/* Farm Management Section for Owners */}
        {user?.role === 'owner' || user?.role === 'admin' ? (
          <View style={styles.section}>
            <View style={[styles.sectionHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
              <Text style={styles.sectionTitle}>
                {t('farm.management')}
              </Text>
            </View>

            <View style={styles.farmManagementButtons}>

              <TouchableOpacity
                style={[styles.farmManagementButton, { backgroundColor: colors.info }]}
                onPress={() => router.push('/user/list')}
              >
                <RTLView style={{ alignItems: 'center', justifyContent: 'center' }}>
                  <Users size={20} color={colors.white} />
                  <Text style={[styles.farmManagementButtonText, { marginHorizontal: 8 }]}>
                    {t('user.manageUsers')}
                  </Text>
                </RTLView>
              </TouchableOpacity>

              {
                user?.role === 'owner' && <>
                  <TouchableOpacity
                    style={[styles.farmManagementButton]}
                    onPress={() => router.push('/farm/create')}
                  >
                    <RTLView style={{ alignItems: 'center', justifyContent: 'center' }}>
                      <Plus size={20} color={colors.white} />
                      <Text style={[styles.farmManagementButtonText, { marginHorizontal: 8 }]}>
                        {t('farm.add')}
                      </Text>
                    </RTLView>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.farmManagementButton, { backgroundColor: colors.warning }]}
                    onPress={() => router.push('/reports')}
                  >
                    <RTLView style={{ alignItems: 'center', justifyContent: 'center' }}>
                      <BarChart3 size={20} color={colors.white} />
                      <Text style={[styles.farmManagementButtonText, { marginHorizontal: 8 }]}>
                        {t('reports.farmReports')}
                      </Text>
                    </RTLView>
                  </TouchableOpacity>
                </>
              }

            </View>
          </View>
        ) : null}

      </ScrollView>

      {/* Farm Selection Modal */}
      <Modal
        visible={showFarmModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowFarmModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={[styles.modalHeader, isRTL && { flexDirection: 'row-reverse' }]}>
              <Text style={styles.modalTitle}>{t('farm.selectFarm')}</Text>
              <TouchableOpacity onPress={() => setShowFarmModal(false)}>
                <Text style={styles.modalCloseText}>{t('common.close')}</Text>
              </TouchableOpacity>
            </View>
            {farms.length > 0 ? (
              <FlatList
                data={farms}
                renderItem={renderFarmItem}
                keyExtractor={item => item.id}
                style={styles.modalList}
              />
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>{t('farm.noFarmsCreateFirst')}</Text>
              </View>
            )}

            {user?.role === 'owner' && (
              <TouchableOpacity
                style={styles.addFarmButton}
                onPress={() => {
                  setShowFarmModal(false);
                  router.push('/farm/create');
                }}
              >
                <View style={[{ alignItems: 'center', flexDirection: isRTL ? 'row-reverse' : 'row', justifyContent: 'center' }, isRTL && { flexDirection: "row-reverse" }]}>
                  <Plus size={20} color={colors.white} />
                  <Text style={[styles.addFarmButtonText, { marginHorizontal: 8 }, isRTL && { marginRight: 8 }]}>
                    {t('farm.addNew')}
                  </Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </Modal>
       <AIAssistantFAB />
    </SafeAreaView>
  );
}

// Import Button component for the no farm view
import Button from '@/components/Button';
import { useLookupStore } from '@/store/lookup-store';
import AIAssistantFAB from '@/components/AIAssistantFAB';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  header: {
    // flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    // justifyContent: 'space-between',
    // alignItems: 'center',
    marginBottom: 16,
  },
  greeting: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 4,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  greetingRTL: {
    textAlign: 'right',
  },
  userName: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[800],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  userNameRTL: {
    textAlign: 'right',
  },
  weatherSummary: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
  },
  temperature: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    marginLeft: I18nManager.isRTL ? 0 : 8,
    marginRight: I18nManager.isRTL ? 8 : 0,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  statsSection: {
    marginBottom: 24,
  },
  statsSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginBottom: 12,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  statsSectionTitleRTL: {
    textAlign: 'right',
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  sectionTitleRTL: {
    textAlign: 'right',
  },
  seeAllText: {
    fontSize: 14,
    color: colors.primary,
  },
  weatherContainer: {
    paddingBottom: 8,
  },
  fieldsContainer: {
    paddingBottom: 8,
  },
  emptyContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyFieldContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    width: 200,
    height: 150,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  emptyText: {
    fontSize: 14,
    color: colors.gray[500],
    textAlign: 'center',
  },
  activityItem: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  activityItemRTL: {
    flexDirection: 'row-reverse',
  },
  activityIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.success + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: I18nManager.isRTL ? 0 : 12,
    marginLeft: I18nManager.isRTL ? 12 : 0,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[800],
    marginBottom: 4,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  activityTitleRTL: {
    textAlign: 'right',
  },
  activityTime: {
    fontSize: 12,
    color: colors.gray[500],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  activityTimeRTL: {
    textAlign: 'right',
  },
  addButton: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 2,
  },
  addButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
    marginLeft: I18nManager.isRTL ? 0 : 8,
    marginRight: I18nManager.isRTL ? 8 : 0,
  },
  farmSelectionButton: {
    // flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmIcon: {
    marginRight: I18nManager.isRTL ? 0 : 8,
    marginLeft: I18nManager.isRTL ? 8 : 0,
  },
  farmIconRTL: {
    alignSelf: 'flex-end',
    marginRight: 0,
    marginLeft: 8,
  },
  farmSelectionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    flex: 1,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  farmSelectionTextRTL: {
    textAlign: 'right',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
  },
  modalCloseText: {
    fontSize: 16,
    color: colors.primary,
  },
  modalList: {
    maxHeight: '80%',
  },
  modalItem: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalItemContentRTL: {
    flexDirection: 'row-reverse',
  },
  modalItemIcon: {
    marginRight: 12,
  },
  modalItemIconRTL: {
    marginRight: 0,
    marginLeft: 12,
  },
  modalItemText: {
    fontSize: 16,
    color: colors.gray[800],
    textAlign: 'left',
  },
  modalItemTextRTL: {
    textAlign: 'right',
  },
  emptyListContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyListText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray[700],
  },
  noFarmContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    padding: 20,
  },
  noFarmTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: colors.gray[800],
    marginTop: 16,
    marginBottom: 8,
  },
  noFarmText: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: 'center',
    marginBottom: 24,
  },
  createFarmButton: {
    width: '80%',
  },
  farmOverviewCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: colors.gray[400],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmOverviewHeader: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  farmOverviewHeaderRTL: {
    flexDirection: 'row-reverse',
  },
  farmOverviewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.gray[800],
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  farmOverviewTitleRTL: {
    textAlign: 'right',
  },
  farmDetailsButton: {
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  farmDetailsText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
  farmOverviewInfo: {
    marginBottom: 16,
  },
  farmInfoItem: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    marginBottom: 4,
  },
  farmInfoItemRTL: {
    flexDirection: 'row-reverse',
  },
  farmInfoLabel: {
    fontSize: 14,
    color: colors.gray[600],
    width: 80,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  farmInfoLabelRTL: {
    textAlign: 'right',
  },
  farmInfoValue: {
    fontSize: 14,
    color: colors.gray[800],
    flex: 1,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  farmInfoValueRTL: {
    textAlign: 'right',
  },
  farmQuickStats: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    justifyContent: 'space-between',
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
  },
  farmQuickStatsRTL: {
    flexDirection: 'row-reverse',
  },
  farmQuickStatItem: {
    alignItems: 'center',
  },
  farmQuickStatValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[800],
    marginVertical: 4,
  },
  farmQuickStatLabel: {
    fontSize: 12,
    color: colors.gray[600],
  },
  farmManagementButtons: {
    flexDirection: 'column',
    gap: 12,
  },
  farmManagementButton: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 12,
  },
  farmManagementButtonRTL: {
    flexDirection: 'row-reverse',
  },
  farmManagementButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.white,
    marginLeft: I18nManager.isRTL ? 0 : 8,
    marginRight: I18nManager.isRTL ? 8 : 0,
  },
  farmManagementButtonTextRTL: {
    marginLeft: 8,
    marginRight: 0,
  },
  addFarmButton: {
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    borderRadius: 8,
    padding: 12,
    margin: 16,
  },
  addFarmButtonRTL: {
    flexDirection: 'row-reverse',
  },
  addFarmButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.white,
    marginLeft: I18nManager.isRTL ? 0 : 8,
    marginRight: I18nManager.isRTL ? 8 : 0,
  },
  addFarmButtonTextRTL: {
    marginLeft: 8,
    marginRight: 0,
  },
});


// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.gray[50],
//   },
//   createFarmButton: {
//     backgroundColor: colors.primary,
//     borderRadius: 8,
//     paddingVertical: 12,
//     paddingHorizontal: 24,
//     alignItems: 'center',
//     justifyContent: 'center',
//     marginTop: 16,
//   },
//   loadingContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: colors.gray[50],
//   },
//   loadingText: {
//     fontSize: 16,
//     color: colors.gray[600],
//     marginTop: 12,
//     textAlign: 'center',
//   },
//   noFarmContainer: {
//     flex: 1,
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: colors.gray[50],
//     padding: 24,
//   },
//   noFarmTitle: {
//     fontSize: 20,
//     fontWeight: '700',
//     color: colors.gray[800],
//     marginBottom: 8,
//     textAlign: 'center',
//   },
//   noFarmText: {
//     fontSize: 14,
//     color: colors.gray[600],
//     marginBottom: 16,
//     textAlign: 'center',
//   },
//   scrollContent: {
//     padding: 16,
//     paddingBottom: 40,
//   },
//   header: {
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     marginBottom: 16,
//   },
//   greeting: {
//     fontSize: 14,
//     color: colors.gray[600],
//     marginBottom: 4,
//   },
//   userName: {
//     fontSize: 20,
//     fontWeight: '700',
//     color: colors.gray[800],
//   },
//   weatherSummary: {
//     alignItems: 'center',
//   },
//   temperature: {
//     fontSize: 18,
//     fontWeight: '600',
//     color: colors.gray[800],
//     marginHorizontal: 8,
//   },
//   statsContainer: {
//     flexDirection: 'row',
//     flexWrap: 'wrap',
//     justifyContent: 'space-between',
//     marginBottom: 24,
//   },
//   statsSection: {
//     marginBottom: 24,
//   },
//   statsSectionTitle: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     marginBottom: 12,
//   },
//   section: {
//     marginBottom: 24,
//   },
//   sectionHeader: {
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     marginBottom: 12,
//   },
//   sectionTitle: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//   },
//   seeAllText: {
//     fontSize: 14,
//     color: colors.primary,
//   },
//   weatherContainer: {
//     paddingBottom: 8,
//   },
//   fieldsContainer: {
//     paddingBottom: 8,
//   },
//   emptyContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     alignItems: 'center',
//     justifyContent: 'center',
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   emptyFieldContainer: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     alignItems: 'center',
//     justifyContent: 'center',
//     width: 200,
//     height: 150,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   emptyText: {
//     fontSize: 14,
//     color: colors.gray[500],
//     textAlign: 'center',
//   },
//   activityItem: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 12,
//     marginBottom: 8,
//     alignItems: 'center',
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   activityIconContainer: {
//     width: 40,
//     height: 40,
//     borderRadius: 20,
//     backgroundColor: colors.success + '20',
//     justifyContent: 'center',
//     alignItems: 'center',
//     marginHorizontal: 12,
//   },
//   activityContent: {
//     flex: 1,
//   },
//   activityTitle: {
//     fontSize: 14,
//     fontWeight: '500',
//     color: colors.gray[800],
//     marginBottom: 4,
//   },
//   activityTime: {
//     fontSize: 12,
//     color: colors.gray[500],
//   },
//   farmSelectionButton: {
//     alignItems: 'center',
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 12,
//     marginBottom: 16,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   farmIcon: {
//     marginHorizontal: 8,
//   },
//   farmSelectionText: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     flex: 1,
//   },
//   modalOverlay: {
//     flex: 1,
//     backgroundColor: 'rgba(0, 0, 0, 0.5)',
//     justifyContent: 'flex-end',
//   },
//   modalContent: {
//     backgroundColor: colors.white,
//     borderTopLeftRadius: 20,
//     borderTopRightRadius: 20,
//     paddingBottom: 20,
//     maxHeight: '70%',
//   },
//   modalHeader: {
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     padding: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   modalTitle: {
//     fontSize: 18,
//     fontWeight: '600',
//     color: colors.gray[800],
//   },
//   modalCloseText: {
//     fontSize: 16,
//     color: colors.primary,
//   },
//   modalList: {
//     maxHeight: '80%',
//   },
//   modalItem: {
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     padding: 16,
//     borderBottomWidth: 1,
//     borderBottomColor: colors.gray[200],
//   },
//   modalItemContent: {
//     alignItems: 'center',
//   },
//   modalItemIcon: {
//     marginHorizontal: 8,
//   },
//   modalItemText: {
//     fontSize: 16,
//     color: colors.gray[800],
//   },
//   farmOverviewCard: {
//     backgroundColor: colors.white,
//     borderRadius: 12,
//     padding: 16,
//     marginBottom: 24,
//     shadowColor: colors.gray[400],
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },
//   farmOverviewHeader: {
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     marginBottom: 12,
//   },
//   farmOverviewTitle: {
//     fontSize: 18,
//     fontWeight: '600',
//     color: colors.gray[800],
//   },
//   farmDetailsButton: {
//     backgroundColor: colors.primary + '20',
//     paddingHorizontal: 12,
//     paddingVertical: 6,
//     borderRadius: 6,
//   },
//   farmDetailsText: {
//     fontSize: 14,
//     color: colors.primary,
//     fontWeight: '500',
//   },
//   farmOverviewInfo: {
//     marginBottom: 16,
//   },
//   farmInfoItem: {
//     marginBottom: 4,
//     alignItems: 'center',
//   },
//   farmInfoLabel: {
//     fontSize: 14,
//     color: colors.gray[600],
//     width: 80,
//   },
//   farmInfoValue: {
//     fontSize: 14,
//     color: colors.gray[800],
//     flex: 1,
//   },
//   farmQuickStats: {
//     justifyContent: 'space-between',
//     backgroundColor: colors.gray[50],
//     borderRadius: 8,
//     padding: 12,
//   },
//   farmQuickStatItem: {
//     alignItems: 'center',
//   },
//   farmQuickStatValue: {
//     fontSize: 16,
//     fontWeight: '600',
//     color: colors.gray[800],
//     marginVertical: 4,
//   },
//   farmQuickStatLabel: {
//     fontSize: 12,
//     color: colors.gray[600],
//   },
//   farmManagementButtons: {
//     flexDirection: 'column',
//     gap: 12,
//   },
//   farmManagementButton: {
//     alignItems: 'center',
//     justifyContent: 'center',
//     backgroundColor: colors.primary,
//     borderRadius: 8,
//     padding: 12,
//   },
//   farmManagementButtonText: {
//     fontSize: 16,
//     fontWeight: '500',
//     color: colors.white,
//   },
//   addFarmButton: {
//     alignItems: 'center',
//     justifyContent: 'center',
//     backgroundColor: colors.primary,
//     borderRadius: 8,
//     padding: 12,
//     margin: 16,
//   },
//   addFarmButtonText: {
//     fontSize: 16,
//     fontWeight: '500',
//     color: colors.white,
//   },
// });
